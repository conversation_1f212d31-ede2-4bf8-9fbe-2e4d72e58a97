#!/bin/bash

echo "=== DeviceDetectionDataService 测试脚本 ==="

# 编译项目
echo "正在编译项目..."
./gradlew compileJava

if [ $? -ne 0 ]; then
    echo "编译失败，退出测试"
    exit 1
fi

echo "编译成功"

# 运行DeviceDetectionDataService测试
echo "正在运行DeviceDetectionDataService测试..."
java --module-path build/classes/java/main:$(find ~/.gradle/caches -name "*.jar" | grep -E "(slf4j|logback|sqlite)" | tr '\n' ':') \
     --add-modules java.sql \
     -cp build/classes/java/main \
     com.logictrue.test.DeviceDetectionDataServiceTest

echo ""
echo "=== 测试完成 ==="

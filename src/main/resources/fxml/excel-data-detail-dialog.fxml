<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.logictrue.controller.ExcelDataDetailDialogController">
   <children>
      <!-- 标题栏 -->
      <HBox alignment="CENTER_LEFT" spacing="10.0">
         <children>
            <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Excel数据详情" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="closeButton" mnemonicParsing="false" text="关闭" />
         </children>
         <VBox.margin>
            <Insets bottom="10.0" left="20.0" right="20.0" top="20.0" />
         </VBox.margin>
      </HBox>

      <!-- 主要内容区域 -->
      <TabPane fx:id="tabPane" VBox.vgrow="ALWAYS">
         <!-- 基本信息选项卡 -->
         <Tab text="基本信息" closable="false">
            <content>
               <ScrollPane fitToWidth="true">
                  <content>
                     <VBox spacing="10.0">
                        <padding>
                           <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                        </padding>
                        <children>
                           <!-- 基本信息表格 -->
                           <GridPane hgap="10.0" vgap="10.0">
                              <columnConstraints>
                                 <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                                 <ColumnConstraints hgrow="ALWAYS" />
                                 <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                                 <ColumnConstraints hgrow="ALWAYS" />
                              </columnConstraints>
                              <children>
                                 <Label text="记录ID:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                                 <Label fx:id="recordIdLabel" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                                 <Label text="设备编码:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                                 <Label fx:id="deviceCodeLabel" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                                 <Label text="模板名称:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                                 <Label fx:id="templateNameLabel" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                                 <Label text="文件名:" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                                 <Label fx:id="fileNameLabel" GridPane.columnIndex="3" GridPane.rowIndex="1" />

                                 <Label text="文件路径:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                                 <Label fx:id="filePathLabel" GridPane.columnIndex="1" GridPane.rowIndex="2" GridPane.columnSpan="3" />

                                 <Label text="文件大小:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                                 <Label fx:id="fileSizeLabel" GridPane.columnIndex="1" GridPane.rowIndex="3" />

                                 <Label text="解析状态:" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                                 <Label fx:id="parseStatusLabel" GridPane.columnIndex="3" GridPane.rowIndex="3" />

                                 <Label text="解析时间:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                                 <Label fx:id="parseTimeLabel" GridPane.columnIndex="1" GridPane.rowIndex="4" />

                                 <Label text="创建时间:" GridPane.columnIndex="2" GridPane.rowIndex="4" />
                                 <Label fx:id="createTimeLabel" GridPane.columnIndex="3" GridPane.rowIndex="4" />

                                 <Label text="总Sheet数:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                                 <Label fx:id="totalSheetsLabel" GridPane.columnIndex="1" GridPane.rowIndex="5" />

                                 <Label text="已解析Sheet数:" GridPane.columnIndex="2" GridPane.rowIndex="5" />
                                 <Label fx:id="parsedSheetsLabel" GridPane.columnIndex="3" GridPane.rowIndex="5" />

                                 <Label text="基础字段数:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                                 <Label fx:id="basicFieldsCountLabel" GridPane.columnIndex="1" GridPane.rowIndex="6" />

                                 <Label text="表格行数:" GridPane.columnIndex="2" GridPane.rowIndex="6" />
                                 <Label fx:id="tableRowsCountLabel" GridPane.columnIndex="3" GridPane.rowIndex="6" />

                                 <Label text="解析消息:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                                 <Label fx:id="parseMessageLabel" GridPane.columnIndex="1" GridPane.rowIndex="7" GridPane.columnSpan="3" />

                                 <Label text="备注:" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                                 <Label fx:id="remarkLabel" GridPane.columnIndex="1" GridPane.rowIndex="8" GridPane.columnSpan="3" />
                              </children>
                           </GridPane>
                        </children>
                     </VBox>
                  </content>
               </ScrollPane>
            </content>
         </Tab>

         <!-- 详情数据选项卡 -->
         <Tab text="详情数据" closable="false">
            <content>
               <ScrollPane fitToWidth="true">
                  <content>
                     <VBox spacing="15.0">
                        <padding>
                           <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                        </padding>
                        <children>
                           <!-- Sheet选择 -->
                           <HBox alignment="CENTER_LEFT" spacing="10.0">
                              <children>
                                 <Label text="选择Sheet:" />
                                 <ComboBox fx:id="detailDataSheetComboBox" prefWidth="200.0" />
                              </children>
                           </HBox>

                           <!-- 基础字段区域 -->
                           <VBox fx:id="basicFieldsSection" spacing="10.0">
                              <children>
                                 <Label style="-fx-font-size: 14px; -fx-font-weight: bold;" text="基础字段" />
                                 <Separator />
                                 <!-- 基础字段内容区域 -->
                                 <VBox fx:id="basicFieldsContent" spacing="8.0" />
                              </children>
                           </VBox>

                           <!-- 表格数据区域 -->
                           <VBox fx:id="tableDataSection" spacing="10.0" VBox.vgrow="ALWAYS">
                              <children>
                                 <HBox alignment="CENTER_LEFT" spacing="10.0">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-font-weight: bold;" text="表格数据" />
                                       <Region HBox.hgrow="ALWAYS" />
                                       <Label fx:id="detailTableDataCountLabel" text="共 0 行数据" />
                                    </children>
                                 </HBox>
                                 <Separator />
                                 <!-- 表格数据 -->
                                 <TableView fx:id="detailTableDataTable" VBox.vgrow="ALWAYS">
                                    <!-- 动态列将在代码中添加 -->
                                 </TableView>
                              </children>
                           </VBox>
                        </children>
                     </VBox>
                  </content>
               </ScrollPane>
            </content>
         </Tab>
      </TabPane>
   </children>
</VBox>

package com.logictrue.test;

import com.logictrue.service.DeviceDetectionDataService;
import com.logictrue.service.DeviceDetectionDataService.DeviceDetectionDataRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

/**
 * DeviceDetectionDataService测试类
 * 演示如何在解析前插入记录获取ID，解析完成后更新状态
 */
public class DeviceDetectionDataServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(DeviceDetectionDataServiceTest.class);
    
    public static void main(String[] args) {
        logger.info("开始DeviceDetectionDataService功能测试");
        
        DeviceDetectionDataService service = new DeviceDetectionDataService();
        
        // 测试完整的解析流程
        testCompleteParsingFlow(service);
        
        // 测试解析失败场景
        testParsingFailureFlow(service);
        
        logger.info("DeviceDetectionDataService功能测试完成");
    }
    
    /**
     * 测试完整的解析流程
     */
    private static void testCompleteParsingFlow(DeviceDetectionDataService service) {
        logger.info("=== 测试完整解析流程 ===");
        
        // 1. 解析前插入记录获取ID
        String deviceCode = "DEVICE_001";
        String templateId = "TEMPLATE_001";
        String templateName = "温度传感器检测模板";
        String fileName = "temperature_data_20241211.xlsx";
        String filePath = "/data/excel/" + fileName;
        Long fileSize = getFileSize(filePath);
        String createBy = "system";
        String remark = "自动解析测试";
        
        Long recordId = service.insertBeforeParsing(deviceCode, templateId, templateName, 
                                                   fileName, filePath, fileSize, createBy, remark);
        
        if (recordId != null) {
            logger.info("✓ 成功插入记录，获取ID: {}", recordId);
            
            // 2. 模拟解析过程 - 更新状态为进行中
            boolean inProgressResult = service.updateParsingInProgress(recordId);
            logger.info("✓ 更新解析状态为进行中: {}", inProgressResult);
            
            // 模拟解析耗时
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 3. 解析完成后更新状态
            Integer parseStatus = 1; // 解析成功
            String parseMessage = "解析完成，共处理3个工作表";
            Integer totalSheets = 3;
            Integer parsedSheets = 3;
            Integer basicFieldsCount = 15;
            Integer tableRowsCount = 120;
            String updateBy = "parser";
            
            boolean updateResult = service.updateAfterParsing(recordId, parseStatus, parseMessage,
                                                             totalSheets, parsedSheets, 
                                                             basicFieldsCount, tableRowsCount, updateBy);
            
            if (updateResult) {
                logger.info("✓ 成功更新解析完成状态");
                
                // 4. 查询记录验证结果
                DeviceDetectionDataRecord record = service.getById(recordId);
                if (record != null) {
                    logger.info("✓ 查询记录成功:");
                    logger.info("  - ID: {}", record.getId());
                    logger.info("  - 设备编码: {}", record.getDeviceCode());
                    logger.info("  - 文件名: {}", record.getFileName());
                    logger.info("  - 解析状态: {}", record.getParseStatus());
                    logger.info("  - 解析消息: {}", record.getParseMessage());
                    logger.info("  - 总工作表数: {}", record.getTotalSheets());
                    logger.info("  - 已解析工作表数: {}", record.getParsedSheets());
                    logger.info("  - 基础字段数: {}", record.getBasicFieldsCount());
                    logger.info("  - 表格行数: {}", record.getTableRowsCount());
                    logger.info("  - 创建时间: {}", record.getCreateTime());
                    logger.info("  - 更新时间: {}", record.getUpdateTime());
                } else {
                    logger.error("✗ 查询记录失败");
                }
            } else {
                logger.error("✗ 更新解析完成状态失败");
            }
        } else {
            logger.error("✗ 插入记录失败");
        }
    }
    
    /**
     * 测试解析失败场景
     */
    private static void testParsingFailureFlow(DeviceDetectionDataService service) {
        logger.info("=== 测试解析失败流程 ===");
        
        // 1. 解析前插入记录
        String deviceCode = "DEVICE_002";
        String templateId = "TEMPLATE_002";
        String templateName = "压力传感器检测模板";
        String fileName = "pressure_data_invalid.xlsx";
        String filePath = "/data/excel/" + fileName;
        Long fileSize = 0L; // 模拟空文件
        String createBy = "system";
        String remark = "解析失败测试";
        
        Long recordId = service.insertBeforeParsing(deviceCode, templateId, templateName, 
                                                   fileName, filePath, fileSize, createBy, remark);
        
        if (recordId != null) {
            logger.info("✓ 成功插入记录，获取ID: {}", recordId);
            
            // 2. 模拟解析失败
            String errorMessage = "文件格式错误：无法读取Excel文件";
            boolean failureResult = service.updateParsingFailed(recordId, errorMessage);
            
            if (failureResult) {
                logger.info("✓ 成功更新解析失败状态");
                
                // 3. 查询记录验证结果
                DeviceDetectionDataRecord record = service.getById(recordId);
                if (record != null) {
                    logger.info("✓ 查询失败记录成功:");
                    logger.info("  - ID: {}", record.getId());
                    logger.info("  - 设备编码: {}", record.getDeviceCode());
                    logger.info("  - 文件名: {}", record.getFileName());
                    logger.info("  - 解析状态: {}", record.getParseStatus());
                    logger.info("  - 错误消息: {}", record.getParseMessage());
                } else {
                    logger.error("✗ 查询失败记录失败");
                }
            } else {
                logger.error("✗ 更新解析失败状态失败");
            }
        } else {
            logger.error("✗ 插入记录失败");
        }
    }
    
    /**
     * 获取文件大小（模拟）
     */
    private static Long getFileSize(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                return file.length();
            } else {
                // 模拟文件大小
                return (long) (Math.random() * 1024 * 1024); // 随机1MB以内
            }
        } catch (Exception e) {
            return 0L;
        }
    }
}

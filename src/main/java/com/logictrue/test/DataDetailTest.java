package com.logictrue.test;

import com.logictrue.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

/**
 * 数据详情功能测试类
 * 用于插入模拟数据并验证数据库功能
 */
public class DataDetailTest {
    private static final Logger logger = LoggerFactory.getLogger(DataDetailTest.class);
    
    public static void main(String[] args) {
        logger.info("开始数据详情功能测试");
        
        DatabaseService databaseService = DatabaseService.getInstance();
        
        // 插入测试数据
        insertTestData(databaseService);
        
        // 验证分页查询功能
        testPaginationQuery(databaseService);
        
        logger.info("数据详情功能测试完成");
    }
    
    /**
     * 插入测试数据
     */
    private static void insertTestData(DatabaseService databaseService) {
        logger.info("开始插入测试数据");
        
        String[] fileNames = {
            "sensor_data_001.json", "temperature_log_002.csv", "pressure_reading_003.xml",
            "humidity_data_004.json", "vibration_log_005.csv", "flow_rate_006.xml",
            "voltage_reading_007.json", "current_data_008.csv", "power_log_009.xml",
            "speed_sensor_010.json", "position_data_011.csv", "acceleration_012.xml",
            "gyroscope_013.json", "magnetometer_014.csv", "barometer_015.xml",
            "light_sensor_016.json", "proximity_017.csv", "sound_level_018.xml",
            "gas_sensor_019.json", "ph_value_020.csv", "conductivity_021.xml",
            "turbidity_022.json", "dissolved_oxygen_023.csv", "chlorine_024.xml",
            "nitrogen_025.json", "phosphorus_026.csv", "potassium_027.xml",
            "calcium_028.json", "magnesium_029.csv", "sulfur_030.xml"
        };
        
        String[] basePaths = {
            "/data/sensors/", "/logs/temperature/", "/readings/pressure/",
            "/data/humidity/", "/logs/vibration/", "/readings/flow/",
            "/data/electrical/", "/logs/current/", "/readings/power/",
            "/data/motion/", "/logs/position/", "/readings/acceleration/"
        };
        
        Random random = new Random();
        LocalDateTime baseTime = LocalDateTime.now().minusDays(30);
        
        int successCount = 0;
        for (int i = 0; i < fileNames.length; i++) {
            String fileName = fileNames[i];
            String basePath = basePaths[i % basePaths.length];
            String filePath = basePath + fileName;
            
            // 生成随机的采集时间（过去30天内）
            LocalDateTime collectTime = baseTime.plusMinutes(random.nextInt(30 * 24 * 60));
            
            boolean success = databaseService.insertRecord(fileName, collectTime, filePath);
            if (success) {
                successCount++;
                logger.debug("插入测试数据成功: {}", fileName);
            } else {
                logger.error("插入测试数据失败: {}", fileName);
            }
            
            // 添加小延迟，避免时间戳完全相同
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        logger.info("测试数据插入完成，成功插入 {} 条记录", successCount);
    }
    
    /**
     * 测试分页查询功能
     */
    private static void testPaginationQuery(DatabaseService databaseService) {
        logger.info("开始测试分页查询功能");
        
        // 获取总记录数
        int totalCount = databaseService.getTotalCount();
        logger.info("数据库总记录数: {}", totalCount);
        
        // 测试不同页面大小的分页查询
        int[] pageSizes = {5, 10, 20};
        
        for (int pageSize : pageSizes) {
            logger.info("测试页面大小: {}", pageSize);
            
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            logger.info("总页数: {}", totalPages);
            
            // 测试前3页（如果存在）
            for (int page = 1; page <= Math.min(3, totalPages); page++) {
                var records = databaseService.getRecords(page, pageSize);
                logger.info("第 {} 页查询结果: {} 条记录", page, records.size());
                
                // 打印前几条记录的详细信息
                for (int i = 0; i < Math.min(2, records.size()); i++) {
                    var record = records.get(i);
                    logger.info("  记录 {}: ID={}, 文件名={}, 采集时间={}, 路径={}",
                            i + 1, record.getId(), record.getFileName(),
                            record.getCollectTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                            record.getFilePath());
                }
            }
        }
        
        logger.info("分页查询功能测试完成");
    }
}

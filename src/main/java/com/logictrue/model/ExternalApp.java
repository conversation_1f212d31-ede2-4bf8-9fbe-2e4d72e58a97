package com.logictrue.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 外部应用程序数据模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExternalApp {
    
    private String id;              // 应用唯一标识
    private String name;            // 应用显示名称
    private String path;            // 应用程序路径
    private String arguments;       // 启动参数
    private String workingDir;      // 工作目录
    private String description;     // 应用描述
    
    public ExternalApp() {
    }
    
    public ExternalApp(String id, String name, String path) {
        this.id = id;
        this.name = name;
        this.path = path;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getArguments() {
        return arguments;
    }
    
    public void setArguments(String arguments) {
        this.arguments = arguments;
    }
    
    public String getWorkingDir() {
        return workingDir;
    }
    
    public void setWorkingDir(String workingDir) {
        this.workingDir = workingDir;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "ExternalApp{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", arguments='" + arguments + '\'' +
                ", workingDir='" + workingDir + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ExternalApp that = (ExternalApp) o;
        return id != null ? id.equals(that.id) : that.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}

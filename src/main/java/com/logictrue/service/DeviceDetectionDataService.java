package com.logictrue.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.time.LocalDateTime;

/**
 * 设备检测数据服务类
 * 提供DeviceDetectionData在SQLite数据库中的新增和修改操作
 */
public class DeviceDetectionDataService {
    private static final Logger logger = LoggerFactory.getLogger(DeviceDetectionDataService.class);
    
    private static final String DEVICE_DETECTION_DATA_TABLE = "device_detection_data";
    
    private final DatabaseService databaseService;
    
    public DeviceDetectionDataService() {
        this.databaseService = DatabaseService.getInstance();
    }
    
    /**
     * 在解析前插入记录并获取ID
     * 
     * @param deviceCode 设备编码
     * @param templateId 模板ID
     * @param templateName 模板名称
     * @param fileName 文件名
     * @param filePath 文件路径
     * @param fileSize 文件大小
     * @param createBy 创建人
     * @param remark 备注
     * @return 插入记录的ID，失败返回null
     */
    public Long insertBeforeParsing(String deviceCode, String templateId, String templateName, 
                                   String fileName, String filePath, Long fileSize, 
                                   String createBy, String remark) {
        String sql = "INSERT INTO " + DEVICE_DETECTION_DATA_TABLE + " (" +
                "device_code, template_id, template_name, file_name, file_path, file_size, " +
                "parse_status, create_by, remark, create_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = DriverManager.getConnection("jdbc:sqlite:" + databaseService.getDbPath());
             PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            pstmt.setString(1, deviceCode);
            pstmt.setString(2, templateId);
            pstmt.setString(3, templateName);
            pstmt.setString(4, fileName);
            pstmt.setString(5, filePath);
            pstmt.setObject(6, fileSize);
            pstmt.setInt(7, 0); // 0-待解析
            pstmt.setString(8, createBy);
            pstmt.setString(9, remark);
            pstmt.setTimestamp(10, Timestamp.valueOf(LocalDateTime.now()));
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        Long id = generatedKeys.getLong(1);
                        logger.info("成功插入设备检测数据记录，ID: {}, 文件: {}", id, fileName);
                        return id;
                    }
                }
            }
            
        } catch (SQLException e) {
            logger.error("插入设备检测数据记录失败: {}", fileName, e);
        }
        
        return null;
    }
    
    /**
     * 解析完成后更新状态
     * 
     * @param id 记录ID
     * @param parseStatus 解析状态 (0-待解析，1-解析成功，2-解析失败)
     * @param parseMessage 解析消息
     * @param totalSheets 总工作表数
     * @param parsedSheets 已解析工作表数
     * @param basicFieldsCount 基础字段数量
     * @param tableRowsCount 表格行数
     * @param updateBy 更新人
     * @return 更新是否成功
     */
    public boolean updateAfterParsing(Long id, Integer parseStatus, String parseMessage,
                                     Integer totalSheets, Integer parsedSheets, 
                                     Integer basicFieldsCount, Integer tableRowsCount,
                                     String updateBy) {
        String sql = "UPDATE " + DEVICE_DETECTION_DATA_TABLE + " SET " +
                "parse_status = ?, parse_message = ?, parse_time = ?, " +
                "total_sheets = ?, parsed_sheets = ?, basic_fields_count = ?, " +
                "table_rows_count = ?, update_by = ?, update_time = ? " +
                "WHERE id = ?";
        
        try (Connection conn = DriverManager.getConnection("jdbc:sqlite:" + databaseService.getDbPath());
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setObject(1, parseStatus);
            pstmt.setString(2, parseMessage);
            pstmt.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()));
            pstmt.setObject(4, totalSheets);
            pstmt.setObject(5, parsedSheets);
            pstmt.setObject(6, basicFieldsCount);
            pstmt.setObject(7, tableRowsCount);
            pstmt.setString(8, updateBy);
            pstmt.setTimestamp(9, Timestamp.valueOf(LocalDateTime.now()));
            pstmt.setLong(10, id);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                logger.info("成功更新设备检测数据记录状态，ID: {}, 状态: {}", id, parseStatus);
                return true;
            } else {
                logger.warn("未找到要更新的记录，ID: {}", id);
                return false;
            }
            
        } catch (SQLException e) {
            logger.error("更新设备检测数据记录状态失败，ID: {}", id, e);
            return false;
        }
    }
    
    /**
     * 根据ID获取记录
     * 
     * @param id 记录ID
     * @return 记录信息，未找到返回null
     */
    public DeviceDetectionDataRecord getById(Long id) {
        String sql = "SELECT id, device_code, template_id, template_name, file_name, file_path, " +
                "file_size, parse_status, parse_message, parse_time, total_sheets, parsed_sheets, " +
                "basic_fields_count, table_rows_count, create_by, create_time, update_by, " +
                "update_time, remark FROM " + DEVICE_DETECTION_DATA_TABLE + " WHERE id = ?";
        
        try (Connection conn = DriverManager.getConnection("jdbc:sqlite:" + databaseService.getDbPath());
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setLong(1, id);
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToRecord(rs);
                }
            }
            
        } catch (SQLException e) {
            logger.error("根据ID获取设备检测数据记录失败，ID: {}", id, e);
        }
        
        return null;
    }
    
    /**
     * 更新解析状态为进行中
     * 
     * @param id 记录ID
     * @return 更新是否成功
     */
    public boolean updateParsingInProgress(Long id) {
        return updateParseStatus(id, 0, "解析进行中");
    }
    
    /**
     * 更新解析状态为成功
     * 
     * @param id 记录ID
     * @param message 成功消息
     * @return 更新是否成功
     */
    public boolean updateParsingSuccess(Long id, String message) {
        return updateParseStatus(id, 1, message != null ? message : "解析成功");
    }
    
    /**
     * 更新解析状态为失败
     * 
     * @param id 记录ID
     * @param errorMessage 错误消息
     * @return 更新是否成功
     */
    public boolean updateParsingFailed(Long id, String errorMessage) {
        return updateParseStatus(id, 2, errorMessage != null ? errorMessage : "解析失败");
    }
    
    /**
     * 更新解析状态
     * 
     * @param id 记录ID
     * @param status 状态
     * @param message 消息
     * @return 更新是否成功
     */
    private boolean updateParseStatus(Long id, Integer status, String message) {
        String sql = "UPDATE " + DEVICE_DETECTION_DATA_TABLE + " SET " +
                "parse_status = ?, parse_message = ?, parse_time = ?, update_time = ? " +
                "WHERE id = ?";
        
        try (Connection conn = DriverManager.getConnection("jdbc:sqlite:" + databaseService.getDbPath());
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, status);
            pstmt.setString(2, message);
            pstmt.setTimestamp(3, Timestamp.valueOf(LocalDateTime.now()));
            pstmt.setTimestamp(4, Timestamp.valueOf(LocalDateTime.now()));
            pstmt.setLong(5, id);
            
            int affectedRows = pstmt.executeUpdate();
            if (affectedRows > 0) {
                logger.info("成功更新解析状态，ID: {}, 状态: {}, 消息: {}", id, status, message);
                return true;
            } else {
                logger.warn("未找到要更新的记录，ID: {}", id);
                return false;
            }
            
        } catch (SQLException e) {
            logger.error("更新解析状态失败，ID: {}", id, e);
            return false;
        }
    }
    
    /**
     * 将ResultSet映射为记录对象
     */
    private DeviceDetectionDataRecord mapResultSetToRecord(ResultSet rs) throws SQLException {
        DeviceDetectionDataRecord record = new DeviceDetectionDataRecord();
        record.setId(rs.getLong("id"));
        record.setDeviceCode(rs.getString("device_code"));
        record.setTemplateId(rs.getString("template_id"));
        record.setTemplateName(rs.getString("template_name"));
        record.setFileName(rs.getString("file_name"));
        record.setFilePath(rs.getString("file_path"));
        record.setFileSize(rs.getObject("file_size", Long.class));
        record.setParseStatus(rs.getObject("parse_status", Integer.class));
        record.setParseMessage(rs.getString("parse_message"));
        
        Timestamp parseTime = rs.getTimestamp("parse_time");
        if (parseTime != null) {
            record.setParseTime(parseTime.toLocalDateTime());
        }
        
        record.setTotalSheets(rs.getObject("total_sheets", Integer.class));
        record.setParsedSheets(rs.getObject("parsed_sheets", Integer.class));
        record.setBasicFieldsCount(rs.getObject("basic_fields_count", Integer.class));
        record.setTableRowsCount(rs.getObject("table_rows_count", Integer.class));
        record.setCreateBy(rs.getString("create_by"));
        
        Timestamp createTime = rs.getTimestamp("create_time");
        if (createTime != null) {
            record.setCreateTime(createTime.toLocalDateTime());
        }
        
        record.setUpdateBy(rs.getString("update_by"));
        
        Timestamp updateTime = rs.getTimestamp("update_time");
        if (updateTime != null) {
            record.setUpdateTime(updateTime.toLocalDateTime());
        }
        
        record.setRemark(rs.getString("remark"));
        
        return record;
    }
    
    /**
     * 设备检测数据记录类
     */
    public static class DeviceDetectionDataRecord {
        private Long id;
        private String deviceCode;
        private String templateId;
        private String templateName;
        private String fileName;
        private String filePath;
        private Long fileSize;
        private Integer parseStatus;
        private String parseMessage;
        private LocalDateTime parseTime;
        private Integer totalSheets;
        private Integer parsedSheets;
        private Integer basicFieldsCount;
        private Integer tableRowsCount;
        private String createBy;
        private LocalDateTime createTime;
        private String updateBy;
        private LocalDateTime updateTime;
        private String remark;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getDeviceCode() { return deviceCode; }
        public void setDeviceCode(String deviceCode) { this.deviceCode = deviceCode; }
        
        public String getTemplateId() { return templateId; }
        public void setTemplateId(String templateId) { this.templateId = templateId; }
        
        public String getTemplateName() { return templateName; }
        public void setTemplateName(String templateName) { this.templateName = templateName; }
        
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
        
        public Integer getParseStatus() { return parseStatus; }
        public void setParseStatus(Integer parseStatus) { this.parseStatus = parseStatus; }
        
        public String getParseMessage() { return parseMessage; }
        public void setParseMessage(String parseMessage) { this.parseMessage = parseMessage; }
        
        public LocalDateTime getParseTime() { return parseTime; }
        public void setParseTime(LocalDateTime parseTime) { this.parseTime = parseTime; }
        
        public Integer getTotalSheets() { return totalSheets; }
        public void setTotalSheets(Integer totalSheets) { this.totalSheets = totalSheets; }
        
        public Integer getParsedSheets() { return parsedSheets; }
        public void setParsedSheets(Integer parsedSheets) { this.parsedSheets = parsedSheets; }
        
        public Integer getBasicFieldsCount() { return basicFieldsCount; }
        public void setBasicFieldsCount(Integer basicFieldsCount) { this.basicFieldsCount = basicFieldsCount; }
        
        public Integer getTableRowsCount() { return tableRowsCount; }
        public void setTableRowsCount(Integer tableRowsCount) { this.tableRowsCount = tableRowsCount; }
        
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }
        
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
        
        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }
        
        public LocalDateTime getUpdateTime() { return updateTime; }
        public void setUpdateTime(LocalDateTime updateTime) { this.updateTime = updateTime; }
        
        public String getRemark() { return remark; }
        public void setRemark(String remark) { this.remark = remark; }
    }
}

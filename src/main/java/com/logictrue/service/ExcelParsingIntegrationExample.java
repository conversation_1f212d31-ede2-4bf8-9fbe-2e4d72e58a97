package com.logictrue.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.LocalDateTime;

/**
 * Excel解析集成示例
 * 展示如何在Excel解析流程中集成DeviceDetectionDataService
 */
public class ExcelParsingIntegrationExample {
    private static final Logger logger = LoggerFactory.getLogger(ExcelParsingIntegrationExample.class);
    
    private final DeviceDetectionDataService detectionDataService;
    
    public ExcelParsingIntegrationExample() {
        this.detectionDataService = new DeviceDetectionDataService();
    }
    
    /**
     * 解析Excel文件的完整流程示例
     * 
     * @param deviceCode 设备编码
     * @param templateId 模板ID
     * @param templateName 模板名称
     * @param excelFilePath Excel文件路径
     * @param createBy 创建人
     * @return 解析是否成功
     */
    public boolean parseExcelFile(String deviceCode, String templateId, String templateName,
                                 String excelFilePath, String createBy) {
        
        logger.info("开始解析Excel文件: {}", excelFilePath);
        
        // 1. 获取文件信息
        File excelFile = new File(excelFilePath);
        if (!excelFile.exists()) {
            logger.error("Excel文件不存在: {}", excelFilePath);
            return false;
        }
        
        String fileName = excelFile.getName();
        Long fileSize = excelFile.length();
        String remark = "Excel文件解析 - " + LocalDateTime.now().toString();
        
        // 2. 解析前插入记录获取ID
        Long recordId = detectionDataService.insertBeforeParsing(
                deviceCode, templateId, templateName, 
                fileName, excelFilePath, fileSize, 
                createBy, remark);
        
        if (recordId == null) {
            logger.error("插入解析记录失败，终止解析流程");
            return false;
        }
        
        logger.info("成功插入解析记录，ID: {}", recordId);
        
        // 3. 更新状态为解析进行中
        detectionDataService.updateParsingInProgress(recordId);
        
        try {
            // 4. 执行实际的Excel解析逻辑
            ExcelParsingResult result = performExcelParsing(excelFilePath, templateId);
            
            // 5. 根据解析结果更新状态
            if (result.isSuccess()) {
                // 解析成功，更新详细信息
                boolean updateResult = detectionDataService.updateAfterParsing(
                        recordId, 
                        1, // 解析成功
                        result.getMessage(),
                        result.getTotalSheets(),
                        result.getParsedSheets(),
                        result.getBasicFieldsCount(),
                        result.getTableRowsCount(),
                        createBy
                );
                
                if (updateResult) {
                    logger.info("Excel解析完成，记录已更新，ID: {}", recordId);
                    return true;
                } else {
                    logger.error("更新解析结果失败，ID: {}", recordId);
                    return false;
                }
            } else {
                // 解析失败，更新错误信息
                detectionDataService.updateParsingFailed(recordId, result.getErrorMessage());
                logger.error("Excel解析失败: {}", result.getErrorMessage());
                return false;
            }
            
        } catch (Exception e) {
            // 解析过程中发生异常
            String errorMessage = "解析过程中发生异常: " + e.getMessage();
            detectionDataService.updateParsingFailed(recordId, errorMessage);
            logger.error("Excel解析异常", e);
            return false;
        }
    }
    
    /**
     * 执行实际的Excel解析逻辑（模拟实现）
     * 在实际项目中，这里应该调用真正的Excel解析服务
     */
    private ExcelParsingResult performExcelParsing(String excelFilePath, String templateId) {
        logger.info("执行Excel解析逻辑，文件: {}, 模板: {}", excelFilePath, templateId);
        
        try {
            // 模拟解析耗时
            Thread.sleep(2000);
            
            // 模拟解析结果
            ExcelParsingResult result = new ExcelParsingResult();
            result.setSuccess(true);
            result.setMessage("解析成功完成");
            result.setTotalSheets(3);
            result.setParsedSheets(3);
            result.setBasicFieldsCount(18);
            result.setTableRowsCount(150);
            
            return result;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            ExcelParsingResult result = new ExcelParsingResult();
            result.setSuccess(false);
            result.setErrorMessage("解析被中断");
            return result;
        } catch (Exception e) {
            ExcelParsingResult result = new ExcelParsingResult();
            result.setSuccess(false);
            result.setErrorMessage("解析异常: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 批量解析Excel文件示例
     */
    public void batchParseExcelFiles(String[] filePaths, String deviceCode, 
                                   String templateId, String templateName, String createBy) {
        logger.info("开始批量解析Excel文件，共{}个文件", filePaths.length);
        
        int successCount = 0;
        int failureCount = 0;
        
        for (String filePath : filePaths) {
            try {
                boolean success = parseExcelFile(deviceCode, templateId, templateName, filePath, createBy);
                if (success) {
                    successCount++;
                    logger.info("文件解析成功: {}", filePath);
                } else {
                    failureCount++;
                    logger.error("文件解析失败: {}", filePath);
                }
            } catch (Exception e) {
                failureCount++;
                logger.error("文件解析异常: {}", filePath, e);
            }
            
            // 添加间隔，避免数据库压力过大
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        logger.info("批量解析完成，成功: {}, 失败: {}", successCount, failureCount);
    }
    
    /**
     * Excel解析结果类
     */
    public static class ExcelParsingResult {
        private boolean success;
        private String message;
        private String errorMessage;
        private Integer totalSheets;
        private Integer parsedSheets;
        private Integer basicFieldsCount;
        private Integer tableRowsCount;
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Integer getTotalSheets() { return totalSheets; }
        public void setTotalSheets(Integer totalSheets) { this.totalSheets = totalSheets; }
        
        public Integer getParsedSheets() { return parsedSheets; }
        public void setParsedSheets(Integer parsedSheets) { this.parsedSheets = parsedSheets; }
        
        public Integer getBasicFieldsCount() { return basicFieldsCount; }
        public void setBasicFieldsCount(Integer basicFieldsCount) { this.basicFieldsCount = basicFieldsCount; }
        
        public Integer getTableRowsCount() { return tableRowsCount; }
        public void setTableRowsCount(Integer tableRowsCount) { this.tableRowsCount = tableRowsCount; }
    }
    
    /**
     * 使用示例的主方法
     */
    public static void main(String[] args) {
        ExcelParsingIntegrationExample example = new ExcelParsingIntegrationExample();
        
        // 单个文件解析示例
        boolean result = example.parseExcelFile(
                "DEVICE_001", 
                "TEMPLATE_001", 
                "温度传感器检测模板",
                "/data/excel/temperature_data.xlsx",
                "system"
        );
        
        System.out.println("解析结果: " + (result ? "成功" : "失败"));
        
        // 批量文件解析示例
        String[] filePaths = {
                "/data/excel/file1.xlsx",
                "/data/excel/file2.xlsx",
                "/data/excel/file3.xlsx"
        };
        
        example.batchParseExcelFiles(filePaths, "DEVICE_002", "TEMPLATE_002", 
                                   "压力传感器检测模板", "system");
    }
}

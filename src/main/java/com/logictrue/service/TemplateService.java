package com.logictrue.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.config.ConfigManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 模板服务类
 * 负责从web接口下载JSON模板并保存到本地
 */
public class TemplateService {
    private static final Logger logger = LoggerFactory.getLogger(TemplateService.class);

    private final ConfigManager configManager;
    private final ObjectMapper objectMapper;
    private final String templateStorageDir;

    public TemplateService() {
        this.configManager = ConfigManager.getInstance();
        this.objectMapper = new ObjectMapper();
        this.templateStorageDir = getTemplateStorageDirectory();

        // 确保模板存储目录存在
        createTemplateStorageDirectory();
    }

    /**
     * 获取模板存储目录路径
     */
    private String getTemplateStorageDirectory() {
        try {
            // 获取jar包所在目录
            String jarDir = getJarDirectory();
            String templateDir = configManager.getTemplateStoragePath();

            if (templateDir == null || templateDir.trim().isEmpty()) {
                templateDir = "templates";
            }

            String fullPath = jarDir + File.separator + templateDir;
            logger.info("模板存储目录路径: {}", fullPath);
            return fullPath;
        } catch (Exception e) {
            logger.error("获取模板存储目录失败", e);
            String fallbackPath = System.getProperty("user.dir") + File.separator + "templates";
            logger.warn("使用备用模板存储路径: {}", fallbackPath);
            return fallbackPath;
        }
    }

    /**
     * 获取jar包所在目录
     */
    private String getJarDirectory() {
        try {
            String jarPath = TemplateService.class.getProtectionDomain()
                    .getCodeSource().getLocation().toURI().getPath();
            File jarFile = new File(jarPath);

            logger.debug("检测到的jar路径: {}", jarPath);

            if (jarFile.isFile()) {
                // 运行的是jar包
                String parentDir = jarFile.getParent();
                logger.info("运行环境: JAR包，jar包目录: {}", parentDir);
                return parentDir;
            } else {
                // 开发环境，使用项目根目录
                String userDir = System.getProperty("user.dir");
                logger.info("运行环境: 开发环境，使用用户目录: {}", userDir);
                return userDir;
            }
        } catch (Exception e) {
            logger.error("获取jar包目录失败", e);
            String fallbackDir = System.getProperty("user.dir");
            logger.warn("使用备用目录: {}", fallbackDir);
            return fallbackDir;
        }
    }

    /**
     * 创建模板存储目录
     */
    private void createTemplateStorageDirectory() {
        try {
            Path templatePath = Paths.get(templateStorageDir);
            logger.debug("检查模板存储目录是否存在: {}", templateStorageDir);

            if (!Files.exists(templatePath)) {
                logger.info("模板存储目录不存在，开始创建: {}", templateStorageDir);
                Files.createDirectories(templatePath);
                logger.info("模板存储目录创建成功: {}", templateStorageDir);
            } else {
                logger.debug("模板存储目录已存在: {}", templateStorageDir);
            }

            // 验证目录权限
            if (!Files.isWritable(templatePath)) {
                logger.warn("模板存储目录不可写: {}", templateStorageDir);
            } else {
                logger.debug("模板存储目录权限正常: {}", templateStorageDir);
            }

        } catch (Exception e) {
            logger.error("创建模板存储目录失败: {}", templateStorageDir, e);
        }
    }

    /**
     * 异步从指定URL直接下载模板文件
     */
    public CompletableFuture<String> downloadTemplateFromUrl(String downloadUrl) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("开始从指定URL下载模板文件: {}", downloadUrl);

                if (downloadUrl == null || downloadUrl.trim().isEmpty()) {
                    logger.warn("下载URL为空，无法下载模板");
                    return null;
                }

                // 直接下载文件流
                logger.info("开始下载文件流，URL: {}", downloadUrl);
                byte[] fileContent = downloadFileStream(downloadUrl);

                if (fileContent == null || fileContent.length == 0) {
                    logger.error("下载模板文件失败，内容为空，URL: {}", downloadUrl);
                    return null;
                }

                logger.info("文件流下载成功，URL: {}, 内容大小: {} 字节", downloadUrl, fileContent.length);

                // 从URL中提取文件名，如果无法提取则使用默认命名
                String fileName = extractFileNameFromUrl(downloadUrl);
                if (fileName == null || fileName.trim().isEmpty()) {
                    fileName = "template_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".json";
                }

                logger.info("使用文件名: {}", fileName);

                // 转换为字符串并保存到本地文件
                String jsonContent = new String(fileContent, "UTF-8");
                logger.debug("模板JSON内容预览: {}...",
                        jsonContent.length() > 200 ? jsonContent.substring(0, 200) : jsonContent);

                String filePath = saveTemplateToFile(fileName, jsonContent);

                logger.info("模板文件下载并保存成功，URL: {}, 保存路径: {}", downloadUrl, filePath);
                return filePath;

            } catch (Exception e) {
                logger.error("从URL下载模板文件失败: {}", downloadUrl, e);
                return null;
            }
        });
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        try {
            // 尝试从URL路径中提取文件名
            java.net.URI uri = new java.net.URI(url);
            String path = uri.getPath();
            if (path != null && !path.isEmpty()) {
                int lastSlash = path.lastIndexOf('/');
                if (lastSlash >= 0 && lastSlash < path.length() - 1) {
                    String fileName = path.substring(lastSlash + 1);
                    // 确保文件名以.json结尾
                    if (!fileName.toLowerCase().endsWith(".json")) {
                        fileName += ".json";
                    }
                    return fileName;
                }
            }
        } catch (Exception e) {
            logger.debug("从URL提取文件名失败: {}", url, e);
        }

        // 如果无法从URL提取，返回null，调用方会使用默认命名
        return null;
    }

    /**
     * 保存模板JSON到本地文件
     */
    private String saveTemplateToFile(String fileName, String templateJson) throws IOException {
        logger.info("开始保存模板文件，文件名: {}", fileName);
        logger.debug("模板内容长度: {} 字符", templateJson.length());

        Path filePath = Paths.get(templateStorageDir, fileName);
        logger.info("保存路径: {}", filePath.toString());

        // 确保父目录存在
        Path parentDir = filePath.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            logger.info("创建父目录: {}", parentDir);
            Files.createDirectories(parentDir);
        }

        // 写入文件
        byte[] contentBytes = templateJson.getBytes("UTF-8");
        Files.write(filePath, contentBytes);

        // 验证文件是否成功保存
        if (Files.exists(filePath)) {
            long fileSize = Files.size(filePath);
            logger.info("模板文件保存成功，文件大小: {} 字节 ({} KB)", fileSize, fileSize / 1024);
            logger.debug("文件绝对路径: {}", filePath.toAbsolutePath());
        } else {
            logger.error("模板文件保存失败，文件不存在: {}", filePath);
            throw new IOException("文件保存失败");
        }

        return filePath.toString();
    }

    /**
     * 下载文件流
     */
    private byte[] downloadFileStream(String urlString) throws IOException {
        logger.info("开始下载文件流，URL: {}", urlString);

        java.net.URI uri;
        try {
            uri = new java.net.URI(urlString);
        } catch (java.net.URISyntaxException e) {
            throw new IOException("无效的URL格式: " + urlString, e);
        }
        URL url = uri.toURL();
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("GET");
        connection.setRequestProperty("Accept", "application/octet-stream");
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(30000);

        logger.debug("发送HTTP请求，连接超时: 10秒, 读取超时: 30秒");

        int responseCode = connection.getResponseCode();
        logger.info("HTTP响应状态码: {}", responseCode);

        if (responseCode < 200 || responseCode >= 300) {
            logger.error("文件下载失败，状态码: {}, URL: {}", responseCode, urlString);

            // 尝试读取错误响应
            try (InputStream errorStream = connection.getErrorStream()) {
                if (errorStream != null) {
                    String errorResponse = new String(errorStream.readAllBytes(), "UTF-8");
                    logger.error("错误响应内容: {}", errorResponse);
                }
            } catch (Exception e) {
                logger.debug("读取错误响应失败", e);
            }

            return null;
        }

        // 获取响应头信息
        String contentType = connection.getContentType();
        String contentDisposition = connection.getHeaderField("Content-Disposition");
        long contentLength = connection.getContentLengthLong();

        logger.info("响应头信息 - Content-Type: {}, Content-Disposition: {}, Content-Length: {}",
                contentType, contentDisposition, contentLength);

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            long totalBytesRead = 0;

            logger.debug("开始读取文件流数据...");

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;

                // 每读取1MB记录一次进度
                if (totalBytesRead % (1024 * 1024) == 0) {
                    logger.debug("已读取数据: {} MB", totalBytesRead / (1024 * 1024));
                }
            }

            byte[] result = outputStream.toByteArray();
            logger.info("文件流下载完成，总大小: {} 字节 ({} KB)", result.length, result.length / 1024);

            return result;
        }
    }

    /**
     * 获取本地已下载的模板文件列表（按修改时间排序）
     */
    public List<String> getLocalTemplateFiles() {
        List<String> templateFiles = new ArrayList<>();

        try {
            Path templatePath = Paths.get(templateStorageDir);
            logger.debug("检查本地模板文件目录: {}", templateStorageDir);

            if (Files.exists(templatePath) && Files.isDirectory(templatePath)) {
                logger.debug("模板目录存在，开始扫描文件");

                // 获取所有.json文件并按修改时间排序
                List<Path> jsonFiles = Files.list(templatePath)
                        .filter(path -> path.toString().toLowerCase().endsWith(".json"))
                        .filter(Files::isRegularFile)
                        .sorted((p1, p2) -> {
                            try {
                                return Files.getLastModifiedTime(p1).compareTo(Files.getLastModifiedTime(p2));
                            } catch (Exception e) {
                                logger.debug("比较文件修改时间失败", e);
                                return 0;
                            }
                        })
                        .collect(java.util.stream.Collectors.toList());

                for (Path jsonFile : jsonFiles) {
                    String filePath = jsonFile.toString();
                    templateFiles.add(filePath);

                    try {
                        long fileSize = Files.size(jsonFile);
                        java.time.LocalDateTime lastModified = java.time.LocalDateTime.ofInstant(
                                Files.getLastModifiedTime(jsonFile).toInstant(),
                                java.time.ZoneId.systemDefault());

                        logger.debug("找到模板文件: {}, 大小: {} 字节, 修改时间: {}",
                                jsonFile.getFileName(), fileSize, lastModified);
                    } catch (Exception e) {
                        logger.debug("获取文件信息失败: {}", jsonFile, e);
                    }
                }

                logger.info("本地模板文件扫描完成，共找到 {} 个文件", templateFiles.size());
            } else {
                logger.info("模板目录不存在或不是目录: {}", templateStorageDir);
            }
        } catch (Exception e) {
            logger.error("获取本地模板文件列表失败", e);
        }

        return templateFiles;
    }

    /**
     * 检查本地是否有可用的模板文件
     */
    public boolean hasLocalTemplateFiles() {
        List<String> localFiles = getLocalTemplateFiles();
        boolean hasFiles = !localFiles.isEmpty();

        logger.info("本地模板文件检查结果: {}, 文件数量: {}",
                hasFiles ? "有可用文件" : "无可用文件", localFiles.size());

        return hasFiles;
    }

    /**
     * 获取最新的本地模板文件路径
     */
    public String getLatestLocalTemplateFile() {
        List<String> localFiles = getLocalTemplateFiles();

        if (localFiles.isEmpty()) {
            logger.warn("本地没有可用的模板文件");
            return null;
        }

        // 返回最后一个（最新的）文件
        String latestFile = localFiles.get(localFiles.size() - 1);
        logger.info("获取最新的本地模板文件: {}", latestFile);

        return latestFile;
    }


    /**
     * 模板信息类
     */
    public static class TemplateInfo {
        private Long templateId;
        private String templateName;
        private String templateCode;
        private String deviceType;
        private String description;

        // Getters and Setters
        public Long getTemplateId() {
            return templateId;
        }

        public void setTemplateId(Long templateId) {
            this.templateId = templateId;
        }

        public String getTemplateName() {
            return templateName;
        }

        public void setTemplateName(String templateName) {
            this.templateName = templateName;
        }

        public String getTemplateCode() {
            return templateCode;
        }

        public void setTemplateCode(String templateCode) {
            this.templateCode = templateCode;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        @Override
        public String toString() {
            return templateName + " (" + templateCode + ")";
        }
    }
}
